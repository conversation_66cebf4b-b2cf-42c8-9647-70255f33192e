#!/bin/bash

echo "=== PostgreSQL Configuration Check ==="
echo ""

echo "1. Checking if PostgreSQL is running..."
if command -v systemctl &> /dev/null; then
    systemctl status postgresql --no-pager -l
elif command -v service &> /dev/null; then
    service postgresql status
else
    echo "Cannot determine PostgreSQL status (no systemctl or service command)"
fi

echo ""
echo "2. Checking PostgreSQL processes..."
ps aux | grep postgres | grep -v grep

echo ""
echo "3. Checking what's listening on port 5432..."
if command -v netstat &> /dev/null; then
    netstat -tlnp | grep 5432
elif command -v ss &> /dev/null; then
    ss -tlnp | grep 5432
else
    echo "Cannot check port 5432 (no netstat or ss command)"
fi

echo ""
echo "4. Checking PostgreSQL configuration files..."
echo "Looking for postgresql.conf..."
find /etc -name "postgresql.conf" 2>/dev/null | head -5
find /var/lib -name "postgresql.conf" 2>/dev/null | head -5

echo ""
echo "Looking for pg_hba.conf..."
find /etc -name "pg_hba.conf" 2>/dev/null | head -5
find /var/lib -name "pg_hba.conf" 2>/dev/null | head -5

echo ""
echo "5. Testing direct connection with psql..."
echo "Trying to connect to localhost:5432..."
PGPASSWORD=f9m6Vxgj784xqkLA45w2A psql -h localhost -p 5432 -U postgres -d postgres -c "SELECT version();" 2>&1

echo ""
echo "6. Checking if PostgreSQL is listening on all interfaces..."
PGPASSWORD=f9m6Vxgj784xqkLA45w2A psql -h 127.0.0.1 -p 5432 -U postgres -d postgres -c "SELECT version();" 2>&1

echo ""
echo "=== End of PostgreSQL Configuration Check ==="
