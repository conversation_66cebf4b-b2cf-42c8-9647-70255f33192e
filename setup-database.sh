#!/bin/bash

# TempFly.io Local PostgreSQL Setup Script
# This script sets up the local PostgreSQL database for TempFly.io

set -e

# Configuration
DB_NAME="tempfly_db"
DB_USER="postgres"
DB_PASSWORD="f9m6Vxgj784xqkLA45w2A"
DB_HOST="localhost"
DB_PORT="5432"

echo "Setting up TempFly.io local PostgreSQL database..."

# Check if PostgreSQL is running
if ! pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER; then
    echo "Error: PostgreSQL is not running or not accessible."
    echo "Please ensure PostgreSQL is installed and running on $DB_HOST:$DB_PORT"
    exit 1
fi

# Create database if it doesn't exist
echo "Creating database '$DB_NAME' if it doesn't exist..."
PGPASSWORD=$DB_PASSWORD createdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME 2>/dev/null || echo "Database '$DB_NAME' already exists or creation failed."

# Run the setup SQL script
echo "Setting up database schema and initial data..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f setup-local-postgresql.sql

echo "Database setup completed successfully!"
echo ""
echo "Database connection details:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo ""
echo "You can now start the TempFly.io application."
